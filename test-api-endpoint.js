/**
 * Test the new product form mappings API endpoint
 */

const medicineHandler = require('./src/handlers/medicine-handler')

async function testProductFormMappingsAPI() {
  try {
    console.log('🧪 Testing Product Form Mappings API Endpoint...\n')
    
    // Test the API endpoint
    const response = await medicineHandler.getProductFormMappings()
    
    console.log('📊 API Response:')
    console.log(JSON.stringify(response, null, 2))
    
    // Validate response structure
    const isValidResponse = 
      response.status === 200 &&
      response.body &&
      response.body.success === true &&
      response.body.data &&
      Array.isArray(response.body.data.fullForms) &&
      Array.isArray(response.body.data.shortForms) &&
      typeof response.body.data.mapping === 'object'
    
    if (isValidResponse) {
      console.log('\n✅ API endpoint test passed!')
      console.log(`📈 Total mappings: ${response.body.data.fullForms.length}`)
      console.log(`📋 Sample mappings:`)
      
      const sampleMappings = Object.entries(response.body.data.mapping).slice(0, 5)
      sampleMappings.forEach(([full, short]) => {
        console.log(`   ${full} → ${short}`)
      })
    } else {
      console.log('❌ API endpoint test failed - Invalid response structure')
    }
    
  } catch (error) {
    console.error('❌ API endpoint test failed:', error.message)
    console.error('Stack:', error.stack)
  }
}

testProductFormMappingsAPI()
