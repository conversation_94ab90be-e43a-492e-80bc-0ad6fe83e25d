# ABDM Integration API Documentation

## Overview
This document describes the API endpoints for ABDM (Ayushman Bharat Digital Mission) integration, specifically for ABHA (Ayushman Bharat Health Account) number generation and verification.

## Base URL
```
Development: https://your-app.azurewebsites.net/api/abdm
Production: https://your-prod-app.azurewebsites.net/api/abdm
```

## Authentication
All endpoints require Bearer token authentication.

## API Endpoints

### 1. Initiate ABHA Registration

**Endpoint:** `POST /abdm/registration/init`

**Description:** Initiates ABHA number registration process using mobile number or Aadhaar number.

**Request Body:**
```json
{
  "method": "mobile",  // "mobile" or "aadhaar"
  "value": "**********",  // Mobile number or Aadhaar number
  "patientId": "patient-123"  // Optional: Patient ID to link ABHA
}
```

**Response:**
```json
{
  "success": true,
  "txnId": "transaction-id-123",
  "message": "OTP sent to mobile number"
}
```

**Error Response:**
```json
{
  "error": "Invalid mobile number format",
  "statusCode": 400
}
```

### 2. Verify OTP

**Endpoint:** `POST /abdm/registration/verify-otp`

**Description:** Verifies the OTP sent during registration initiation.

**Request Body:**
```json
{
  "txnId": "transaction-id-123",
  "otp": "123456"
}
```

**Response:**
```json
{
  "success": true,
  "txnId": "transaction-id-123",
  "message": "OTP verified successfully",
  "nextStep": "confirm_registration"
}
```

### 3. Resend OTP

**Endpoint:** `POST /abdm/registration/resend-otp`

**Description:** Resends OTP for ABHA registration.

**Request Body:**
```json
{
  "txnId": "transaction-id-123"
}
```

**Response:**
```json
{
  "success": true,
  "txnId": "new-transaction-id-456",
  "message": "OTP resent successfully"
}
```

### 4. Confirm Registration

**Endpoint:** `POST /abdm/registration/confirm`

**Description:** Confirms ABHA registration after OTP verification.

**Request Body:**
```json
{
  "txnId": "transaction-id-123",
  "userData": {
    "firstName": "John",
    "lastName": "Doe",
    "gender": "M",
    "yearOfBirth": 1990,
    "dayOfBirth": 15,
    "monthOfBirth": 6
  }
}
```

**Response:**
```json
{
  "success": true,
  "healthId": "john.doe@abdm",
  "healthIdNumber": "1234-5678-9012-3456",
  "name": "John Doe",
  "message": "ABHA number generated successfully"
}
```

### 5. Fetch ABHA Details

**Endpoint:** `POST /abdm/details`

**Description:** Fetches ABHA details using ABHA number or mobile number.

**Request Body:**
```json
{
  "type": "healthId",  // "healthId" or "mobile"
  "value": "john.doe@abdm"  // ABHA number or mobile number
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "healthId": "john.doe@abdm",
    "healthIdNumber": "1234-5678-9012-3456",
    "name": "John Doe",
    "gender": "M",
    "yearOfBirth": 1990,
    "dayOfBirth": 15,
    "monthOfBirth": 6,
    "mobile": "**********",
    "status": "ACTIVE"
  }
}
```

### 6. Verify ABHA Number

**Endpoint:** `POST /abdm/verify`

**Description:** Verifies if an ABHA number is valid and active.

**Request Body:**
```json
{
  "healthId": "john.doe@abdm"
}
```

**Response:**
```json
{
  "success": true,
  "valid": true,
  "status": "ACTIVE",
  "message": "ABHA number verification completed"
}
```

### 7. Get Patient's ABHA Record

**Endpoint:** `GET /abdm/patient/{patientId}`

**Description:** Retrieves the ABHA record for a specific patient.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "abha-patient-123",
    "patientId": "patient-123",
    "healthId": "john.doe@abdm",
    "healthIdNumber": "1234-5678-9012-3456",
    "name": "John Doe",
    "gender": "M",
    "yearOfBirth": 1990,
    "mobile": "**********",
    "status": "ACTIVE",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

## Error Codes

| Status Code | Description |
|-------------|-------------|
| 400 | Bad Request - Invalid input parameters |
| 401 | Unauthorized - Invalid or missing authentication |
| 404 | Not Found - Resource not found |
| 409 | Conflict - ABHA already exists for patient |
| 500 | Internal Server Error - Server error |

## Validation Rules

### Mobile Number
- Must be 10 digits
- Must start with 6, 7, 8, or 9
- Format: `^[6-9]\d{9}$`

### Aadhaar Number
- Must be 12 digits
- Format: `^\d{12}$`

### ABHA Number
- Format: `XX-XXXX-XXXX-XXXX` (where X is a digit)
- Example: `12-**************`

## Integration Flow

### ABHA Generation Flow
1. **Initiate Registration** → Get transaction ID
2. **Verify OTP** → Confirm OTP is valid
3. **Confirm Registration** → Generate ABHA number
4. **Store Record** → Save to local database

### ABHA Verification Flow
1. **Input ABHA Number** → User provides ABHA
2. **Verify with ABDM** → Check validity
3. **Return Status** → Confirm if valid/active

### ABHA Details Fetch Flow
1. **Input Identifier** → ABHA number or mobile
2. **Fetch from ABDM** → Get user details
3. **Return Details** → Display user information

## Environment Variables Required

```bash
ABDM_API_BASE_URL=https://dev.abdm.gov.in/gateway
ABDM_CLIENT_ID=your-client-id
ABDM_CLIENT_SECRET=your-client-secret
```

## Testing

Use the provided Postman collection or test with curl:

```bash
# Initiate registration
curl -X POST "https://your-app.azurewebsites.net/api/abdm/registration/init" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{"method":"mobile","value":"**********","patientId":"test-patient"}'
```
