# Product Form Mapping Feature

## Overview

This feature implements a comprehensive mapping system between full product form names and their standardized short forms for medicines. All API responses now return short forms instead of full product form names for better consistency and reduced data size.

## Product Form Mappings

| Product Form | Short Form |
|--------------|------------|
| Capsule | CAP |
| Tablet | TAB |
| Ointment | OINT |
| Injection | INJ |
| Infusion | INF |
| Syrup | SYP |
| Cream | CRM |
| Chewing Gum | GUM |
| Powder | POWDER |
| Eye Drop | E/D |
| Tablet PR | TAB PR |
| Gel | GEL |
| Suspension | SUSP |
| Vaginal Suppository | VAG SUPP |
| Tablet SR | TAB SR |
| Oral Drop | DROP |
| Oral Suspension | ORAL SUSP |
| Pessary | PESS |
| Ophthalmic Solution | OPH SOL |
| Tablet MD | TAB MD |
| Eye Gel | E/GEL |
| Capsule SR | CAP SR |
| Powder for Injection | POWDER |
| Soft Gelatin Capsule | GEL CAP |
| Tablet DR | TAB DR |
| Kit | KIT |
| Tablet DT | TAB DT |

## Implementation Details

### Core Utility
- **File**: `src/utils/product-form-mapper.js`
- **Purpose**: Central mapping utility with methods for transformation
- **Features**:
  - Case-insensitive mapping
  - Bidirectional conversion (full ↔ short)
  - Batch transformation for arrays
  - Fallback to original value if no mapping exists

### Updated Components

#### 1. Medicine Handler (`src/handlers/medicine-handler.js`)
- **Changes**: 
  - Added product form mapper import
  - Updated `transformMedicinesData()` method
  - Updated `transformOrganizationMedicinesData()` method
  - Added `getProductFormMappings()` method
- **Impact**: All medicine API responses now return short forms in `DrugFormulation` field

#### 2. Prescription Package Service (`src/services/prescription-package-service.js`)
- **Changes**:
  - Added product form mapper import
  - Updated medicine transformation in `getPrescriptionPackageById()`
  - Updated medicine transformation in `createPrescriptionPackage()`
  - Updated medicine transformation in `updatePrescriptionPackage()`
- **Impact**: Prescription packages now store and return short forms in `drugForm` field

#### 3. Medicine API Function (`src/functions/medicine.js`)
- **Changes**: Added new endpoint for getting product form mappings
- **New Endpoint**: `GET /medicines/product-forms`

## API Changes

### Modified Responses

#### Medicine Search API
**Before:**
```json
{
  "id": "MED001",
  "DrugFormulation": "Capsule",
  "BrandName": "Medicine Name",
  ...
}
```

**After:**
```json
{
  "id": "MED001",
  "DrugFormulation": "CAP",
  "BrandName": "Medicine Name",
  ...
}
```

#### Prescription Package API
**Before:**
```json
{
  "medicines": [
    {
      "drugForm": "Tablet",
      "medicineName": "Medicine Name",
      ...
    }
  ]
}
```

**After:**
```json
{
  "medicines": [
    {
      "drugForm": "TAB",
      "medicineName": "Medicine Name",
      ...
    }
  ]
}
```

### New API Endpoint

#### Get Product Form Mappings
**Endpoint**: `GET /medicines/product-forms`

**Response:**
```json
{
  "success": true,
  "data": {
    "fullForms": ["Capsule", "Tablet", "Ointment", ...],
    "shortForms": ["CAP", "TAB", "OINT", ...],
    "mapping": {
      "Capsule": "CAP",
      "Tablet": "TAB",
      "Ointment": "OINT",
      ...
    }
  },
  "message": "Product form mappings retrieved successfully"
}
```

## Utility Methods

### ProductFormMapper Class

#### `getShortForm(productForm)`
- Converts full product form to short form
- Case-insensitive matching
- Returns original value if no mapping found

#### `getFullForm(shortForm)`
- Converts short form back to full product form
- Returns original value if no mapping found

#### `transformMedicineToShortForm(medicine)`
- Transforms a single medicine object
- Handles multiple property names: `productForm`, `DrugFormulation`, `drugForm`

#### `transformMedicinesToShortForm(medicines)`
- Transforms an array of medicine objects
- Applies transformation to each medicine in the array

#### `getAllForms()`
- Returns all available mappings
- Includes full forms array, short forms array, and mapping object

#### `hasMapping(productForm)`
- Checks if a product form has a defined mapping
- Case-insensitive checking

## Benefits

1. **Consistency**: Standardized short forms across all APIs
2. **Data Efficiency**: Reduced response size with shorter form names
3. **Backward Compatibility**: Fallback to original values for unmapped forms
4. **Extensibility**: Easy to add new product form mappings
5. **Case Insensitive**: Handles variations in case formatting
6. **Centralized**: Single source of truth for all mappings

## Usage Examples

### Frontend Integration
```javascript
// Get all mappings
const response = await fetch('/medicines/product-forms');
const { data } = await response.json();

// Use mapping to display full forms in UI
const displayForm = data.mapping[shortForm] || shortForm;
```

### Adding New Mappings
```javascript
// In src/utils/product-form-mapper.js
this.productFormMapping = {
  ...existing mappings,
  'New Product Form': 'NEW_SHORT'
}
```

## Testing

The implementation includes comprehensive testing for:
- Individual form mappings
- Case-insensitive matching
- Medicine object transformation
- Array transformation
- Fallback behavior for unmapped forms

## Migration Notes

- **Breaking Change**: API responses now return short forms instead of full forms
- **Frontend Updates**: Update UI components to handle short forms or use the mapping API
- **Database**: No database changes required - transformation happens at API response level
- **Backward Compatibility**: Original data in database remains unchanged

## Future Enhancements

1. **Dynamic Mappings**: Store mappings in database for runtime updates
2. **Localization**: Support for multiple languages
3. **Custom Mappings**: Organization-specific form mappings
4. **Validation**: API validation to ensure only valid product forms are accepted
5. **Analytics**: Track usage of different product forms
