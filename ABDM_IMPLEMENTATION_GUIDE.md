# ABDM Integration Implementation Guide

## Overview

This guide provides step-by-step instructions for implementing ABDM (Ayushman Bharat Digital Mission) integration for ABHA (Ayushman Bharat Health Account) number generation and verification in your EMR system.

## Prerequisites

1. **ABDM Registration**: Register as a Health Information Provider (HIP) with ABDM
2. **API Credentials**: Obtain Client ID and Client Secret from ABDM
3. **Azure Cosmos DB**: Set up a container for storing ABHA records
4. **Node.js**: Version 14+ with Azure Functions

## Implementation Steps

### 1. Environment Setup

1. **Add Environment Variables**:
   - Copy `abdm.env.example` to `.env`
   - Update with your ABDM credentials:
     ```
     ABDM_API_BASE_URL=https://dev.abdm.gov.in/gateway
     ABDM_CLIENT_ID=your-abdm-client-id
     ABDM_CLIENT_SECRET=your-abdm-client-secret
     ```

2. **Create Database Container**:
   - Run the setup script:
     ```
     node src/scripts/setup-abdm-container.js
     ```

### 2. Backend Implementation

The backend implementation consists of:

1. **ABDM Service** (`src/services/abdm-service.js`):
   - Handles communication with ABDM APIs
   - Manages authentication and token caching
   - Implements all required API calls

2. **ABDM Repository** (`src/repositories/abdm-repository.js`):
   - Manages ABHA records in the database
   - Provides methods for CRUD operations

3. **ABDM Handler** (`src/handlers/abdm-handler.js`):
   - Implements business logic
   - Validates input data
   - Manages transaction flow

4. **API Routes** (`src/functions/abdm.js`):
   - Defines HTTP endpoints
   - Handles authentication
   - Routes requests to handler methods

### 3. Integration Flow

#### ABHA Number Generation

1. **Initiate Registration**:
   - User selects method (Mobile/Aadhaar)
   - System calls `initiateAbhaRegistration`
   - ABDM sends OTP to user's mobile

2. **Verify OTP**:
   - User enters OTP
   - System calls `verifyAbhaOtp`
   - ABDM verifies OTP

3. **Confirm Registration**:
   - System collects user details
   - Calls `confirmAbhaRegistration`
   - ABDM generates ABHA number
   - System stores ABHA record

#### ABHA Verification

1. **Verify ABHA Number**:
   - User enters ABHA number
   - System calls `verifyAbhaNumber`
   - ABDM verifies validity
   - System displays result

#### Fetch ABHA Details

1. **Fetch Details**:
   - User enters ABHA or mobile
   - System calls `fetchAbhaDetails`
   - ABDM returns user details
   - System displays information

### 4. Frontend Implementation

Implement the following UI components:

1. **ABHA Registration Form**:
   - Radio buttons for method selection (Mobile/Aadhaar)
   - Input field for mobile/Aadhaar number
   - OTP input field
   - User details form (name, gender, DOB)
   - Submit buttons

2. **ABHA Verification Form**:
   - Input field for ABHA number
   - Verification button
   - Result display area

3. **ABHA Details Form**:
   - Input field for ABHA/mobile
   - Fetch button
   - Details display area

### 5. Error Handling

Implement comprehensive error handling:

1. **Input Validation**:
   - Mobile format: `^[6-9]\d{9}$`
   - Aadhaar format: `^\d{12}$`
   - ABHA format: Check for valid pattern

2. **API Errors**:
   - Authentication failures
   - Invalid OTP
   - User already exists
   - Network errors

3. **User Feedback**:
   - Clear error messages
   - Retry options
   - Progress indicators

### 6. Testing

1. **Unit Tests**:
   - Test each service method
   - Test validation logic
   - Test error handling

2. **Integration Tests**:
   - Test complete flows
   - Test with ABDM sandbox

3. **Manual Testing**:
   - Test with real mobile numbers
   - Verify OTP reception
   - Confirm ABHA generation

### 7. Security Considerations

1. **Token Management**:
   - Secure storage of tokens
   - Proper token refresh
   - Token expiration handling

2. **Data Protection**:
   - Encrypt sensitive data
   - Implement proper access controls
   - Follow data retention policies

3. **Audit Logging**:
   - Log all ABHA operations
   - Track user actions
   - Monitor for suspicious activity

## Deployment

1. **Development Environment**:
   - Deploy to dev environment
   - Test with ABDM sandbox
   - Verify all flows

2. **Production Environment**:
   - Update environment variables
   - Switch to production ABDM endpoints
   - Monitor initial transactions

## Troubleshooting

### Common Issues

1. **Authentication Failures**:
   - Check client ID and secret
   - Verify token expiration
   - Check for network issues

2. **OTP Issues**:
   - Verify mobile number format
   - Check transaction timeout
   - Ensure proper transaction ID handling

3. **ABHA Generation Failures**:
   - Verify user data format
   - Check for duplicate records
   - Validate transaction flow

## Support

For issues with ABDM integration:
- ABDM Support: [<EMAIL>](mailto:<EMAIL>)
- ABDM Documentation: [https://sandbox.abdm.gov.in/docs](https://sandbox.abdm.gov.in/docs)

## References

1. ABDM API Documentation: [https://sandbox.abdm.gov.in/swagger/](https://sandbox.abdm.gov.in/swagger/)
2. ABHA Number Specifications: [https://abdm.gov.in/publications](https://abdm.gov.in/publications)
3. Azure Functions Documentation: [https://docs.microsoft.com/en-us/azure/azure-functions/](https://docs.microsoft.com/en-us/azure/azure-functions/)
