/**
 * ABDM Configuration
 * Configuration settings for ABDM integration
 */

const abdmConfig = {
  // ABDM API URLs
  development: {
    baseUrl: 'https://dev.abdm.gov.in/gateway',
    tokenEndpoint: 'https://dev.abdm.gov.in/gateway/v0.5/sessions',
    endpoints: {
      initRegistration: '/v1/registration/init',
      verifyOtp: '/v1/auth/otp/verify',
      confirmRegistration: '/v1/registration/confirm',
      fetchDetails: '/v1/healthid/details',
      verifyHealthId: '/v1/verification/healthid'
    }
  },
  
  production: {
    baseUrl: 'https://abdm.gov.in/gateway',
    tokenEndpoint: 'https://abdm.gov.in/gateway/v0.5/sessions',
    endpoints: {
      initRegistration: '/v1/registration/init',
      verifyOtp: '/v1/auth/otp/verify',
      confirmRegistration: '/v1/registration/confirm',
      fetchDetails: '/v1/healthid/details',
      verifyHealthId: '/v1/verification/healthid'
    }
  },

  // Validation patterns
  validation: {
    mobile: /^[6-9]\d{9}$/,
    aadhaar: /^\d{12}$/,
    healthId: /^[0-9]{2}-[0-9]{4}-[0-9]{4}-[0-9]{4}$/
  },

  // Cache settings
  cache: {
    tokenTTL: 55 * 60, // 55 minutes
    transactionTTL: 10 * 60 // 10 minutes
  },

  // Error messages
  errorMessages: {
    invalidMethod: 'Method must be either "mobile" or "aadhaar"',
    invalidMobile: 'Invalid mobile number format. Must be 10 digits starting with 6-9',
    invalidAadhaar: 'Invalid Aadhaar number format. Must be 12 digits',
    invalidHealthId: 'Invalid ABHA number format',
    missingTxnId: 'Transaction ID is required',
    missingOtp: 'OTP is required',
    expiredTransaction: 'Invalid or expired transaction ID',
    otpNotVerified: 'OTP not verified',
    abhaExists: 'ABHA number already exists for this patient',
    abhaNotFound: 'ABHA record not found',
    authenticationFailed: 'ABDM authentication failed',
    registrationFailed: 'ABHA registration failed',
    verificationFailed: 'ABHA verification failed'
  },

  // Success messages
  successMessages: {
    otpSent: 'OTP sent successfully',
    otpVerified: 'OTP verified successfully',
    registrationComplete: 'ABHA number generated successfully',
    detailsFetched: 'ABHA details fetched successfully',
    verificationComplete: 'ABHA number verification completed',
    otpResent: 'OTP resent successfully'
  }
}

/**
 * Get configuration based on environment
 * @returns {Object} Configuration object
 */
function getConfig() {
  const environment = process.env.NODE_ENV || 'development'
  return {
    ...abdmConfig[environment],
    validation: abdmConfig.validation,
    cache: abdmConfig.cache,
    errorMessages: abdmConfig.errorMessages,
    successMessages: abdmConfig.successMessages
  }
}

module.exports = {
  abdmConfig,
  getConfig
}
