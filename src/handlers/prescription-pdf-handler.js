const prescriptionService = require('../services/prescription-service')
const patientService = require('../services/patient-service')
const doctorService = require('../services/doctor-service')
const organizationService = require('../services/admin/organization-service')
const pdfService = require('../services/pdf-service')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')

class PrescriptionPDFHandler {
  /**
   * Generate and download prescription PDF
   */
  async downloadPrescriptionPDF(req) {
    try {
      const prescriptionId = req.query.get('prescriptionId')
      const doctorId = req.query.get('doctorId')
      const organizationId = req.query.get('organizationId')

      // Validate required parameters
      if (!prescriptionId) {
        return jsonResponse(
          'Missing required parameter: prescriptionId',
          HttpStatusCode.BadRequest,
        )
      }

      if (!doctorId) {
        return jsonResponse(
          'Missing required parameter: doctorId',
          HttpStatusCode.BadRequest,
        )
      }

      if (!organizationId) {
        return jsonResponse(
          'Missing required parameter: organizationId',
          HttpStatusCode.BadRequest,
        )
      }

      // Fetch prescription data
      const prescriptionData = await prescriptionService.getPrescriptionById(
        prescriptionId,
      )
      if (!prescriptionData || prescriptionData.length === 0) {
        return jsonResponse('Prescription not found', HttpStatusCode.NotFound)
      }

      const prescription = prescriptionData[0]

      // Fetch patient data using patientId from prescription
      const patientData = await patientService.GetPatientProfile(
        prescription.patientId,
      )
      if (!patientData) {
        return jsonResponse('Patient not found', HttpStatusCode.NotFound)
      }

      const patient = patientData

      // Fetch doctor data
      const doctorData = await doctorService.getDoctor(doctorId)
      if (!doctorData) {
        return jsonResponse('Doctor not found', HttpStatusCode.NotFound)
      }

      const doctor = doctorData

      // Fetch organization data
      const organizationData = await organizationService.getOrganizationById(
        organizationId,
      )
      if (!organizationData) {
        return jsonResponse('Organization not found', HttpStatusCode.NotFound)
      }

      const organization = organizationData

      // Generate PDF
      const pdfBuffer = await pdfService.generatePrescriptionPDF(
        prescription,
        patient,
        doctor,
        organization,
      )

      // Return PDF as response
      return {
        status: 200,
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="prescription-${prescriptionId}.pdf"`,
          'Content-Length': pdfBuffer.byteLength.toString(),
        },
        body: Buffer.from(pdfBuffer),
      }
    } catch (error) {
      console.error('Error generating prescription PDF:', error)
      return jsonResponse(
        'Error generating prescription PDF',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Print prescription PDF (same as download but with different content disposition)
   */
  async printPrescriptionPDF(req) {
    try {
      const prescriptionId = req.query.get('prescriptionId')
      const doctorId = req.query.get('doctorId')
      const organizationId = req.query.get('organizationId')

      // Validate required parameters
      if (!prescriptionId) {
        return jsonResponse(
          'Missing required parameter: prescriptionId',
          HttpStatusCode.BadRequest,
        )
      }

      if (!doctorId) {
        return jsonResponse(
          'Missing required parameter: doctorId',
          HttpStatusCode.BadRequest,
        )
      }

      if (!organizationId) {
        return jsonResponse(
          'Missing required parameter: organizationId',
          HttpStatusCode.BadRequest,
        )
      }

      // Fetch prescription data
      const prescriptionData = await prescriptionService.getPrescriptionById(
        prescriptionId,
      )
      if (!prescriptionData || prescriptionData.length === 0) {
        return jsonResponse('Prescription not found', HttpStatusCode.NotFound)
      }

      const prescription = prescriptionData[0]

      // Fetch patient data using patientId from prescription
      const patientData = await patientService.GetPatientProfile(
        prescription.patientId,
      )
      if (!patientData) {
        return jsonResponse('Patient not found', HttpStatusCode.NotFound)
      }

      const patient = patientData

      // Fetch doctor data
      const doctorData = await doctorService.getDoctor(doctorId)
      if (!doctorData) {
        return jsonResponse('Doctor not found', HttpStatusCode.NotFound)
      }

      const doctor = doctorData

      // Fetch organization data
      const organizationData = await organizationService.getOrganizationById(
        organizationId,
      )
      if (!organizationData) {
        return jsonResponse('Organization not found', HttpStatusCode.NotFound)
      }

      const organization = organizationData

      // Generate PDF
      const pdfBuffer = await pdfService.generatePrescriptionPDF(
        prescription,
        patient,
        doctor,
        organization,
      )

      // Return PDF for printing (inline display)
      return {
        status: 200,
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `inline; filename="prescription-${prescriptionId}.pdf"`,
          'Content-Length': pdfBuffer.byteLength.toString(),
        },
        body: Buffer.from(pdfBuffer),
      }
    } catch (error) {
      console.error('Error generating prescription PDF for print:', error)
      return jsonResponse(
        'Error generating prescription PDF for print',
        HttpStatusCode.InternalServerError,
      )
    }
  }
}

module.exports = new PrescriptionPDFHandler()
