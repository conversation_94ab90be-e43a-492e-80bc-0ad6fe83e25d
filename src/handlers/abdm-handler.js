/**
 * ABDM Handler
 * Handles ABHA number generation, verification, and management
 */

const abdmService = require('../services/abdm-service')
const abdmRepository = require('../repositories/abdm-repository')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const { logInfo, logError } = require('../common/logging')
const NodeCache = require('node-cache')

// Cache for storing temporary transaction data
const transactionCache = new NodeCache({ stdTTL: 600 }) // 10 minutes

class ABDMHandler {
  /**
   * Initiate ABHA number registration
   * @param {Object} req - Request object
   * @returns {Promise<Object>} Response with transaction ID
   */
  async initiateAbhaRegistration(req) {
    try {
      const body = await req.json()
      const { method, value, patientId } = body

      // Validate input
      if (!method || !value) {
        return jsonResponse(
          'Method (mobile/aadhaar) and value are required',
          HttpStatusCode.BadRequest
        )
      }

      if (!['mobile', 'aadhaar'].includes(method)) {
        return jsonResponse(
          'Method must be either "mobile" or "aadhaar"',
          HttpStatusCode.BadRequest
        )
      }

      // Validate mobile number format
      if (method === 'mobile' && !/^[6-9]\d{9}$/.test(value)) {
        return jsonResponse(
          'Invalid mobile number format',
          HttpStatusCode.BadRequest
        )
      }

      // Validate Aadhaar number format
      if (method === 'aadhaar' && !/^\d{12}$/.test(value)) {
        return jsonResponse(
          'Invalid Aadhaar number format',
          HttpStatusCode.BadRequest
        )
      }

      // Check if ABHA already exists for this patient
      if (patientId) {
        const existingAbha = await abdmRepository.getAbhaByPatientId(patientId)
        if (existingAbha) {
          return jsonResponse(
            'ABHA number already exists for this patient',
            HttpStatusCode.Conflict
          )
        }
      }

      // Initiate registration with ABDM
      const result = await abdmService.initiateRegistration(method, value)

      // Store transaction data in cache
      const transactionData = {
        txnId: result.txnId,
        method,
        value,
        patientId,
        timestamp: new Date().toISOString()
      }
      transactionCache.set(result.txnId, transactionData)

      logInfo('ABHA registration initiated', { txnId: result.txnId, method })

      return jsonResponse({
        success: true,
        txnId: result.txnId,
        message: `OTP sent to ${method === 'mobile' ? 'mobile number' : 'registered mobile'}`
      })
    } catch (error) {
      logError('Error initiating ABHA registration:', error)
      return jsonResponse(
        error.message || 'Failed to initiate ABHA registration',
        HttpStatusCode.InternalServerError
      )
    }
  }

  /**
   * Verify OTP for ABHA registration
   * @param {Object} req - Request object
   * @returns {Promise<Object>} Response with verification status
   */
  async verifyAbhaOtp(req) {
    try {
      const body = await req.json()
      const { txnId, otp } = body

      // Validate input
      if (!txnId || !otp) {
        return jsonResponse(
          'Transaction ID and OTP are required',
          HttpStatusCode.BadRequest
        )
      }

      // Get transaction data from cache
      const transactionData = transactionCache.get(txnId)
      if (!transactionData) {
        return jsonResponse(
          'Invalid or expired transaction ID',
          HttpStatusCode.BadRequest
        )
      }

      // Verify OTP with ABDM
      const result = await abdmService.verifyOtp(txnId, otp)

      // Update transaction data
      transactionData.otpVerified = true
      transactionData.verificationData = result
      transactionCache.set(txnId, transactionData)

      logInfo('ABHA OTP verification successful', { txnId })

      return jsonResponse({
        success: true,
        txnId,
        message: 'OTP verified successfully',
        nextStep: 'confirm_registration'
      })
    } catch (error) {
      logError('Error verifying ABHA OTP:', error)
      return jsonResponse(
        error.message || 'OTP verification failed',
        HttpStatusCode.BadRequest
      )
    }
  }

  /**
   * Confirm ABHA registration
   * @param {Object} req - Request object
   * @returns {Promise<Object>} Response with ABHA details
   */
  async confirmAbhaRegistration(req) {
    try {
      const body = await req.json()
      const { txnId, userData } = body

      // Validate input
      if (!txnId) {
        return jsonResponse(
          'Transaction ID is required',
          HttpStatusCode.BadRequest
        )
      }

      // Get transaction data from cache
      const transactionData = transactionCache.get(txnId)
      if (!transactionData || !transactionData.otpVerified) {
        return jsonResponse(
          'Invalid transaction or OTP not verified',
          HttpStatusCode.BadRequest
        )
      }

      // Confirm registration with ABDM
      const result = await abdmService.confirmRegistration(txnId, userData)

      // Save ABHA record to database
      if (transactionData.patientId) {
        const abhaData = {
          patientId: transactionData.patientId,
          healthId: result.healthId,
          healthIdNumber: result.healthIdNumber,
          name: result.name,
          gender: result.gender,
          yearOfBirth: result.yearOfBirth,
          dayOfBirth: result.dayOfBirth,
          monthOfBirth: result.monthOfBirth,
          address: result.address,
          mobile: result.mobile,
          status: 'ACTIVE'
        }

        await abdmRepository.saveAbhaRecord(abhaData)
      }

      // Clear transaction data
      transactionCache.del(txnId)

      logInfo('ABHA registration confirmed', { 
        healthId: result.healthId,
        patientId: transactionData.patientId
      })

      return jsonResponse({
        success: true,
        healthId: result.healthId,
        healthIdNumber: result.healthIdNumber,
        name: result.name,
        message: 'ABHA number generated successfully'
      })
    } catch (error) {
      logError('Error confirming ABHA registration:', error)
      return jsonResponse(
        error.message || 'Failed to confirm ABHA registration',
        HttpStatusCode.InternalServerError
      )
    }
  }

  /**
   * Fetch ABHA details
   * @param {Object} req - Request object
   * @returns {Promise<Object>} Response with ABHA details
   */
  async fetchAbhaDetails(req) {
    try {
      const body = await req.json()
      const { type, value } = body

      // Validate input
      if (!type || !value) {
        return jsonResponse(
          'Type (healthId/mobile) and value are required',
          HttpStatusCode.BadRequest
        )
      }

      if (!['healthId', 'mobile'].includes(type)) {
        return jsonResponse(
          'Type must be either "healthId" or "mobile"',
          HttpStatusCode.BadRequest
        )
      }

      // Fetch details from ABDM
      const result = await abdmService.fetchAbhaDetails(type, value)

      logInfo('ABHA details fetched', { type, value })

      return jsonResponse({
        success: true,
        data: result
      })
    } catch (error) {
      logError('Error fetching ABHA details:', error)
      return jsonResponse(
        error.message || 'Failed to fetch ABHA details',
        HttpStatusCode.InternalServerError
      )
    }
  }

  /**
   * Verify ABHA number
   * @param {Object} req - Request object
   * @returns {Promise<Object>} Response with verification status
   */
  async verifyAbhaNumber(req) {
    try {
      const body = await req.json()
      const { healthId } = body

      // Validate input
      if (!healthId) {
        return jsonResponse(
          'Health ID is required',
          HttpStatusCode.BadRequest
        )
      }

      // Verify with ABDM
      const result = await abdmService.verifyHealthId(healthId)

      logInfo('ABHA number verified', { healthId })

      return jsonResponse({
        success: true,
        valid: result.valid || true,
        status: result.status,
        message: 'ABHA number verification completed'
      })
    } catch (error) {
      logError('Error verifying ABHA number:', error)
      return jsonResponse(
        error.message || 'ABHA verification failed',
        HttpStatusCode.BadRequest
      )
    }
  }

  /**
   * Get patient's ABHA record
   * @param {Object} req - Request object
   * @returns {Promise<Object>} Response with ABHA record
   */
  async getPatientAbha(req) {
    try {
      const { patientId } = req.params

      if (!patientId) {
        return jsonResponse(
          'Patient ID is required',
          HttpStatusCode.BadRequest
        )
      }

      const abhaRecord = await abdmRepository.getAbhaByPatientId(patientId)

      if (!abhaRecord) {
        return jsonResponse(
          'ABHA record not found for this patient',
          HttpStatusCode.NotFound
        )
      }

      return jsonResponse({
        success: true,
        data: abhaRecord
      })
    } catch (error) {
      logError('Error getting patient ABHA record:', error)
      return jsonResponse(
        'Failed to get ABHA record',
        HttpStatusCode.InternalServerError
      )
    }
  }
}

  /**
   * Resend OTP for ABHA registration
   * @param {Object} req - Request object
   * @returns {Promise<Object>} Response with resend status
   */
  async resendAbhaOtp(req) {
    try {
      const body = await req.json()
      const { txnId } = body

      if (!txnId) {
        return jsonResponse(
          'Transaction ID is required',
          HttpStatusCode.BadRequest
        )
      }

      // Get transaction data from cache
      const transactionData = transactionCache.get(txnId)
      if (!transactionData) {
        return jsonResponse(
          'Invalid or expired transaction ID',
          HttpStatusCode.BadRequest
        )
      }

      // Re-initiate registration to resend OTP
      const result = await abdmService.initiateRegistration(
        transactionData.method,
        transactionData.value
      )

      // Update transaction data with new txnId
      transactionData.txnId = result.txnId
      transactionData.timestamp = new Date().toISOString()
      transactionCache.set(result.txnId, transactionData)
      transactionCache.del(txnId) // Remove old transaction

      logInfo('ABHA OTP resent', { oldTxnId: txnId, newTxnId: result.txnId })

      return jsonResponse({
        success: true,
        txnId: result.txnId,
        message: 'OTP resent successfully'
      })
    } catch (error) {
      logError('Error resending ABHA OTP:', error)
      return jsonResponse(
        error.message || 'Failed to resend OTP',
        HttpStatusCode.InternalServerError
      )
    }
  }
}

module.exports = new ABDMHandler()
