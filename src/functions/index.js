const { app, HttpResponse } = require('@azure/functions')
const AuthMessage = require('../common/auth-message')
const { doValidate } = require('../common/user-validation')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const medicineHandler = require('../handlers/medicine-handler')
const finalizePatientRecordsCron = require('../tasks/finalize-patient-history-cron')
const testHandler = require('../handlers/lab-test-handler')
const finalizePatientLifestyleCron = require('../tasks/finalize-records-cron')
const { DefaultRoles } = require('../common/roles')
const userHandler = require('../handlers/user-handler')
const emailService = require('../services/email-service')
const b2cService = require('../services/b2c-service')

finalizePatientRecordsCron()
finalizePatientLifestyleCron()
app.setup({
  enableHttpStream: false,
})

app.hook.appStart(async (context) => {
  console.log('EMR Function start...')
  try {
    const superAdminEmail = '<EMAIL>'
    const superAdminPassword = 'SuperAdminArcaaiEHR@123'
    const hashedPassword = await userHandler.hashPassword(superAdminPassword)

    const superAdmin = {
      accountEnabled: true,
      displayName: 'Super Admin',
      identities: [
        {
          signInType: 'emailAddress',
          issuer: `${process.env.TENANT_NAME}.onmicrosoft.com`,
          issuerAssignedId: superAdminEmail,
        },
      ],
      passwordProfile: {
        forceChangePasswordNextSignIn: true,
        password: superAdminPassword,
      },
      passwordPolicies: 'DisablePasswordExpiration, DisableStrongPassword',
    }

    // Check if the Super Admin already exists in Azure AD B2C
    const existingUser = await userHandler.getUserByEmail(superAdminEmail)
    if (existingUser && existingUser.length > 0) {
      console.log(
        'Super Admin already exists in Azure AD B2C. Skipping creation.',
      )
    } else {
      const result = await b2cService.createB2CUser(superAdmin)
      const resetToken = await emailService.generateActivationToken(
        superAdminEmail,
      )

      const superAdminObj = {
        id: result.id,
        userRole: DefaultRoles.SUPER_ADMIN,
        name: 'Super Admin Arcaai EHR',
        email: superAdminEmail,
        password: hashedPassword,
        resetToken: resetToken,
        isActive: true,
      }

      await userHandler.createUser(superAdminObj, 'system')
      console.log('Super Admin seeded successfully.')
    }

    setImmediate(async () => {
      try {
        // const seedResult = await medicineHandler.seedMedicinesFromExcel()
        // console.log(seedResult.message)
        // const result = await testHandler.seedTestsFromExcel()
        // console.log(result.message)
      } catch (error) {
        console.error('Error while seeding medicines/test:', error.message)
      }
    })
  } catch (error) {
    console.error('Error during app startup:', error.message)
  }
  app.setup({
    enableHttpStream: false,
  })
})

app.hook.appTerminate((context) => {
  console.log('EMR Function stop...')
})

app.hook.preInvocation(async (context) => {
  var req = context.inputs[0]
  const auth = await doValidate(req)
  if (auth.message != AuthMessage.SUCCESS) {
    context.functionHandler = (...args) => {
      return jsonResponse(auth.message, HttpStatusCode.Unauthorized)
    }
  } else {
    context.invocationContext.extraInputs.set('decode', auth.decode)
  }
})
