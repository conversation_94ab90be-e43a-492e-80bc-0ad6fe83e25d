const { app } = require('@azure/functions')
const medicineHandler = require('../handlers/medicine-handler')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const { HttpMethod } = require('../common/constant')

app.http('medicine', {
  methods: ['GET', 'POST'],
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    switch (req.method) {
      case HttpMethod.get:
        try {
          const data = await medicineHandler.getAllMedicines()
          return jsonResponse(data)
        } catch (err) {
          return jsonResponse(
            'Error fetching medicines',
            HttpStatusCode.InternalServerError,
          )
        }

      case HttpMethod.post:
        try {
          const result = await medicineHandler.seedMedicinesFromExcel()
          return jsonResponse(result)
        } catch (err) {
          return jsonResponse(
            'Error seeding medicines',
            HttpStatusCode.InternalServerError,
          )
        }

      default:
        return jsonResponse(
          `Unsupported HTTP method`,
          HttpStatusCode.MethodNotAllowed,
        )
    }
  },
})
app.http('medicine-search', {
  methods: ['POST'],
  route: 'medicine/search',
  authLevel: 'function',
  handler: async (req, context) => {
    switch (req.method) {
      case HttpMethod.post:
        if (!req.body) {
          return jsonResponse(
            `Missing search query payload`,
            HttpStatusCode.BadRequest,
          )
        }
        const body = await req.json()
        const searchText = body?.searchText || ''
        const pageSize = body?.pageSize || 10
        const continuationToken = body?.continuationToken || ''
        const organizationId = body?.organizationId || ''

        if (!searchText || searchText === '') {
          return jsonResponse(`Missing search text`, HttpStatusCode.BadRequest)
        }

        try {
          let data
          if (organizationId) {
            data = await medicineHandler.searchOrganizationMedicines(
              organizationId,
              searchText,
              pageSize,
              continuationToken,
            )
          } else {
            data = await medicineHandler.searchMedicines(
              searchText,
              pageSize,
              continuationToken,
            )
          }
          return jsonResponse(data)
        } catch (err) {
          return jsonResponse(
            'Error searching medicines',
            HttpStatusCode.InternalServerError,
          )
        }

      default:
        return jsonResponse(
          `Unsupported HTTP method`,
          HttpStatusCode.MethodNotAllowed,
        )
    }
  },
})
app.http('organization-medicines', {
  methods: ['GET'],
  route: 'organization/medicines',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return await medicineHandler.fetchMedicinesForOrganization(req)
    } catch (err) {
      return jsonResponse(
        'Error fetching organization medicines',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('organization-medicines-update', {
  methods: ['POST'],
  route: 'organization/medicines/update',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return await medicineHandler.updateOrganizationMedicines(req)
    } catch (err) {
      context.log.error('Error updating organization medicines:', err)
      return jsonResponse(
        'Error updating organization medicines',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('organization-medicines-remove', {
  methods: ['POST'],
  route: 'organization/medicines/remove',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return await medicineHandler.removeOrganizationMedicines(req)
    } catch (err) {
      context.log.error('Error removing organization medicines:', err)
      return jsonResponse(
        'Error removing organization medicines',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('organization-medicines-remove-status', {
  methods: ['GET'],
  route: 'organization/medicines/remove/status/{jobId}',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return await medicineHandler.getRemoveJobStatus(req)
    } catch (err) {
      context.log.error('Error getting remove job status:', err)
      return jsonResponse(
        'Error getting remove job status',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('product-form-mappings', {
  methods: ['GET'],
  route: 'medicines/product-forms',
  authLevel: 'function',
  handler: (req, context) => medicineHandler.getProductFormMappings(),
})
