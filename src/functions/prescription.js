const { app } = require('@azure/functions')
const { HttpMethod } = require('../common/constant')
const prescriptionHandler = require('../handlers/prescription-handler')
const prescriptionPDFHandler = require('../handlers/prescription-pdf-handler')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')

app.http('get-prescriptions', {
  methods: ['GET'],
  route: 'prescriptions',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return prescriptionHandler.getPrescriptions(req)
    } catch (err) {
      return jsonResponse(
        'Error fetching patient prescriptions',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('get-prescription-details', {
  methods: ['GET'],
  route: 'prescriptions/details',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return prescriptionHandler.getPrescriptionDetails(req)
    } catch (err) {
      return jsonResponse(
        'Error fetching prescription details',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('create-update-prescription', {
  methods: ['POST', 'PATCH'],
  route: 'prescriptions',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return prescriptionHandler.createOrUpdatePrescription(req)
    } catch (err) {
      return jsonResponse(
        'Error creating or updating prescription',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('prescription-search', {
  methods: ['POST'],
  route: 'prescriptions/search',
  authLevel: 'function',
  handler: (req, context) => prescriptionHandler.searchPrescriptions(req),
})

app.http('prescription-pdf-download', {
  methods: ['GET'],
  route: 'prescriptions/pdf/download',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return prescriptionPDFHandler.downloadPrescriptionPDF(req)
    } catch (err) {
      return jsonResponse(
        'Error generating prescription PDF',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('prescription-pdf-print', {
  methods: ['GET'],
  route: 'prescriptions/pdf/print',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return prescriptionPDFHandler.printPrescriptionPDF(req)
    } catch (err) {
      return jsonResponse(
        'Error generating prescription PDF for print',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})
