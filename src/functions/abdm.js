/**
 * ABDM API Functions
 * Handles ABHA number generation, verification, and management
 */

const { app } = require('@azure/functions')
const abdmHandler = require('../handlers/abdm-handler')
const { doValidate } = require('../common/user-validation')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const AuthMessage = require('../common/auth-message')

// Initialize ABHA registration (Mobile/Aadhaar)
app.http('initiate-abha-registration', {
  methods: ['POST'],
  route: 'abdm/registration/init',
  authLevel: 'function',
  handler: async (req, context) => {
    const auth = await doValidate(req)
    if (auth.message !== AuthMessage.SUCCESS) {
      return jsonResponse(auth.message, HttpStatusCode.Unauthorized)
    }
    return abdmHandler.initiateAbhaRegistration(req)
  }
})

// Verify OTP for ABHA registration
app.http('verify-abha-otp', {
  methods: ['POST'],
  route: 'abdm/registration/verify-otp',
  authLevel: 'function',
  handler: async (req, context) => {
    const auth = await doValidate(req)
    if (auth.message !== AuthMessage.SUCCESS) {
      return jsonResponse(auth.message, HttpStatusCode.Unauthorized)
    }
    return abdmHandler.verifyAbhaOtp(req)
  }
})

// Resend OTP for ABHA registration
app.http('resend-abha-otp', {
  methods: ['POST'],
  route: 'abdm/registration/resend-otp',
  authLevel: 'function',
  handler: async (req, context) => {
    const auth = await doValidate(req)
    if (auth.message !== AuthMessage.SUCCESS) {
      return jsonResponse(auth.message, HttpStatusCode.Unauthorized)
    }
    return abdmHandler.resendAbhaOtp(req)
  }
})

// Confirm ABHA registration
app.http('confirm-abha-registration', {
  methods: ['POST'],
  route: 'abdm/registration/confirm',
  authLevel: 'function',
  handler: async (req, context) => {
    const auth = await doValidate(req)
    if (auth.message !== AuthMessage.SUCCESS) {
      return jsonResponse(auth.message, HttpStatusCode.Unauthorized)
    }
    return abdmHandler.confirmAbhaRegistration(req)
  }
})

// Fetch ABHA details
app.http('fetch-abha-details', {
  methods: ['POST'],
  route: 'abdm/details',
  authLevel: 'function',
  handler: async (req, context) => {
    const auth = await doValidate(req)
    if (auth.message !== AuthMessage.SUCCESS) {
      return jsonResponse(auth.message, HttpStatusCode.Unauthorized)
    }
    return abdmHandler.fetchAbhaDetails(req)
  }
})

// Verify ABHA number
app.http('verify-abha-number', {
  methods: ['POST'],
  route: 'abdm/verify',
  authLevel: 'function',
  handler: async (req, context) => {
    const auth = await doValidate(req)
    if (auth.message !== AuthMessage.SUCCESS) {
      return jsonResponse(auth.message, HttpStatusCode.Unauthorized)
    }
    return abdmHandler.verifyAbhaNumber(req)
  }
})

// Get patient's ABHA record
app.http('get-patient-abha', {
  methods: ['GET'],
  route: 'abdm/patient/{patientId}',
  authLevel: 'function',
  handler: async (req, context) => {
    const auth = await doValidate(req)
    if (auth.message !== AuthMessage.SUCCESS) {
      return jsonResponse(auth.message, HttpStatusCode.Unauthorized)
    }
    return abdmHandler.getPatientAbha(req)
  }
})
