/**
 * Setup ABDM Container
 * Creates the AbhaRecords container in Cosmos DB
 */

const { CosmosClient } = require('@azure/cosmos')
require('dotenv').config()

// Cosmos DB configuration
const endpoint = process.env.COSMOSDB_ENDPOINT
const key = process.env.COSMOSDB_KEY
const databaseId = process.env.COSMOSDB_DATABASE
const containerId = 'AbhaRecords'

async function setupAbdmContainer() {
  try {
    console.log('Setting up ABDM container...')
    
    // Create Cosmos client
    const client = new CosmosClient({ endpoint, key })
    
    // Get database
    const { database } = await client.databases.createIfNotExists({ id: databaseId })
    console.log(`Database ${databaseId} ready`)
    
    // Create container with partition key
    const containerDef = {
      id: containerId,
      partitionKey: { paths: ['/patientId'] },
      indexingPolicy: {
        indexingMode: 'consistent',
        automatic: true,
        includedPaths: [
          { path: '/patientId/?' },
          { path: '/healthId/?' },
          { path: '/mobile/?' },
          { path: '/status/?' }
        ],
        excludedPaths: [{ path: '/*' }]
      }
    }
    
    const { container } = await database.containers.createIfNotExists(containerDef)
    console.log(`Container ${containerId} ready`)
    
    // Create indexes for common queries
    console.log('Creating indexes...')
    
    // Sample ABHA record for testing
    const sampleRecord = {
      id: 'sample-abha-record',
      patientId: 'sample-patient-id',
      healthId: 'sample-health-id',
      healthIdNumber: '1234-5678-9012-3456',
      name: 'Sample Patient',
      gender: 'M',
      yearOfBirth: 1990,
      dayOfBirth: 1,
      monthOfBirth: 1,
      address: 'Sample Address',
      mobile: '**********',
      status: 'ACTIVE',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    // Create and then delete sample record to ensure indexes
    await container.items.create(sampleRecord)
    console.log('Sample record created')
    
    await container.item(sampleRecord.id, sampleRecord.patientId).delete()
    console.log('Sample record deleted')
    
    console.log('ABDM container setup completed successfully')
  } catch (error) {
    console.error('Error setting up ABDM container:', error)
  }
}

// Run the setup
setupAbdmContainer()
  .then(() => {
    console.log('Setup completed')
    process.exit(0)
  })
  .catch(error => {
    console.error('Setup failed:', error)
    process.exit(1)
  })
