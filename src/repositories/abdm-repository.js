/**
 * ABDM Repository
 * Handles database operations for ABHA-related data
 */

const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const { logError } = require('../common/logging')

// Container for ABHA data
const abhaContainer = 'AbhaRecords'

class ABDMRepository {
  /**
   * Save ABHA record for a patient
   * @param {Object} abhaData - ABHA data to save
   * @returns {Promise<Object>} Saved ABHA record
   */
  async saveAbhaRecord(abhaData) {
    try {
      // Ensure required fields
      if (!abhaData.patientId || !abhaData.healthId) {
        throw new Error('Patient ID and Health ID are required')
      }

      // Create ABHA record
      const abhaRecord = {
        id: `abha-${abhaData.patientId}`,
        patientId: abhaData.patientId,
        healthId: abhaData.healthId,
        healthIdNumber: abhaData.healthIdNumber,
        name: abhaData.name,
        gender: abhaData.gender,
        yearOfBirth: abhaData.yearOfBirth,
        dayOfBirth: abhaData.dayOfBirth,
        monthOfBirth: abhaData.monthOfBirth,
        address: abhaData.address,
        mobile: abhaData.mobile,
        status: abhaData.status || 'ACTIVE',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      return await cosmosDbContext.upsertItem(abhaRecord.id, abhaRecord, abhaContainer)
    } catch (error) {
      logError('Error saving ABHA record:', error)
      throw error
    }
  }

  /**
   * Get ABHA record by patient ID
   * @param {string} patientId - Patient ID
   * @returns {Promise<Object>} ABHA record
   */
  async getAbhaByPatientId(patientId) {
    try {
      const query = `SELECT * FROM c WHERE c.patientId = "${patientId}"`
      const results = await cosmosDbContext.queryItems(query, abhaContainer)
      return results.length > 0 ? results[0] : null
    } catch (error) {
      logError(`Error getting ABHA record for patient ${patientId}:`, error)
      throw error
    }
  }

  /**
   * Get ABHA record by health ID
   * @param {string} healthId - ABHA number
   * @returns {Promise<Object>} ABHA record
   */
  async getAbhaByHealthId(healthId) {
    try {
      const query = `SELECT * FROM c WHERE c.healthId = "${healthId}"`
      const results = await cosmosDbContext.queryItems(query, abhaContainer)
      return results.length > 0 ? results[0] : null
    } catch (error) {
      logError(`Error getting ABHA record for health ID ${healthId}:`, error)
      throw error
    }
  }

  /**
   * Get ABHA record by mobile number
   * @param {string} mobile - Mobile number
   * @returns {Promise<Object>} ABHA record
   */
  async getAbhaByMobile(mobile) {
    try {
      const query = `SELECT * FROM c WHERE c.mobile = "${mobile}"`
      const results = await cosmosDbContext.queryItems(query, abhaContainer)
      return results.length > 0 ? results[0] : null
    } catch (error) {
      logError(`Error getting ABHA record for mobile ${mobile}:`, error)
      throw error
    }
  }

  /**
   * Update ABHA record status
   * @param {string} healthId - ABHA number
   * @param {string} status - New status
   * @returns {Promise<Object>} Updated ABHA record
   */
  async updateAbhaStatus(healthId, status) {
    try {
      const abhaRecord = await this.getAbhaByHealthId(healthId)
      if (!abhaRecord) {
        throw new Error(`ABHA record not found for health ID: ${healthId}`)
      }

      abhaRecord.status = status
      abhaRecord.updatedAt = new Date().toISOString()

      return await cosmosDbContext.upsertItem(abhaRecord.id, abhaRecord, abhaContainer)
    } catch (error) {
      logError(`Error updating ABHA status for ${healthId}:`, error)
      throw error
    }
  }

  /**
   * Delete ABHA record
   * @param {string} patientId - Patient ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteAbhaRecord(patientId) {
    try {
      const abhaRecord = await this.getAbhaByPatientId(patientId)
      if (!abhaRecord) {
        throw new Error(`ABHA record not found for patient ID: ${patientId}`)
      }

      await cosmosDbContext.deleteItem(abhaRecord.id, abhaRecord.id, abhaContainer)
      return true
    } catch (error) {
      logError(`Error deleting ABHA record for patient ${patientId}:`, error)
      throw error
    }
  }
}

module.exports = new ABDMRepository()
