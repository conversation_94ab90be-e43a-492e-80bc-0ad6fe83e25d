const { jsPDF } = require('jspdf')
require('jspdf-autotable')
const path = require('path')
const fs = require('fs')

class PDFService {
  constructor() {
    this.logoPath = path.join(process.cwd(), 'assets', 'hospital-logo.png')
    this.signaturePath = path.join(
      process.cwd(),
      'assets',
      'digital-signature.png',
    )
  }

  /**
   * Generate prescription PDF
   * @param {Object} prescriptionData - Complete prescription data
   * @param {Object} patientData - Patient information
   * @param {Object} doctorData - Doctor information
   * @param {Object} organizationData - Organization information
   * @returns {Buffer} PDF buffer
   */
  async generatePrescriptionPDF(
    prescriptionData,
    patientData,
    doctorData,
    organizationData,
  ) {
    const doc = new jsPDF()

    // Set up document properties
    doc.setProperties({
      title: 'Medical Prescription',
      subject: 'Prescription',
      author: doctorData.general?.fullName || doctorData.name || 'Doctor',
      creator: '<PERSON><PERSON>i EMR System',
    })

    // Add header with logo and organization info
    await this.addHeader(doc, organizationData, doctorData)

    // Add prescription heading and patient details
    this.addPrescriptionHeadingAndPatientInfo(
      doc,
      patientData,
      prescriptionData,
    )

    // Add medicines table
    this.addMedicinesTable(doc, prescriptionData.medicines)

    // Add footer section with signature, total amount and disclaimer
    await this.addFooterSection(doc, prescriptionData.medicines)

    return doc.output('arraybuffer')
  }

  /**
   * Add header with logo and organization info
   */
  async addHeader(doc, organizationData, doctorData) {
    const pageWidth = doc.internal.pageSize.width

    // Left side - Doctor details
    doc.setFontSize(12)
    doc.setFont('helvetica', 'bold')
    doc.text(
      `Dr. ${doctorData.general?.fullName || doctorData.name || 'Doctor Name'}`,
      20,
      20,
    )

    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')

    // Display qualifications
    const qualifications = doctorData.professionalDetails?.qualifications || []
    const qualificationText = qualifications
      .map((q) => q.specialization || q.degree || 'Qualification')
      .join(', ')
    doc.text(
      qualificationText || doctorData.general?.designation || 'Qualification',
      20,
      28,
    )

    doc.text(`Reg. No: ${doctorData.general?.doctorID || 'N/A'}`, 20, 36)

    // Right side - Logo (smaller size)
    if (fs.existsSync(this.logoPath)) {
      try {
        const logoData = fs.readFileSync(this.logoPath, 'base64')
        doc.addImage(logoData, 'PNG', pageWidth - 40, 10, 25, 20)
      } catch (error) {
        console.warn('Could not load logo:', error.message)
      }
    }

    // Right side - Hospital address (below logo)
    doc.setFontSize(9)
    doc.setFont('helvetica', 'normal')
    const rightX = pageWidth - 80

    doc.text(organizationData.name || 'Medical Center', rightX, 35)

    if (organizationData.address) {
      const address =
        typeof organizationData.address === 'string'
          ? organizationData.address
          : `${organizationData.address.street || ''}, ${
              organizationData.address.city || ''
            }`
      doc.text(address, rightX, 42)

      if (organizationData.address.state && organizationData.address.pinCode) {
        doc.text(
          `${organizationData.address.state} ${organizationData.address.pinCode}`,
          rightX,
          49,
        )
      }
    }

    if (organizationData.contactPhone) {
      doc.text(`Ph: ${organizationData.contactPhone}`, rightX, 56)
    }

    // Add thin horizontal line
    doc.setLineWidth(0.05)
    doc.line(20, 65, pageWidth - 20, 65)
  }

  /**
   * Add prescription heading and patient details in one line
   */
  addPrescriptionHeadingAndPatientInfo(doc, patientData, prescriptionData) {
    const pageWidth = doc.internal.pageSize.width
    let yPos = 75

    // Center "PRESCRIPTION" heading
    doc.setFontSize(16)
    doc.setFont('helvetica', 'bold')
    doc.text('Prescription', pageWidth / 2, yPos, { align: 'center' })

    yPos += 15

    // Patient info in one line: Name, ID, Age, Mobile, Date
    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')

    const prescriptionDate = new Date(
      prescriptionData.created_on || prescriptionData.createdAt || Date.now(),
    )

    // Patient info spread across full width
    const patientDetails = [
      `Name: ${patientData.name || 'N/A'}`,
      `ID: ${patientData.id || prescriptionData.patientId || 'N/A'}`,
      `Age: ${this.calculateAge(patientData.dob) || patientData.age || 'N/A'}`,
      `Mobile: ${patientData.contact?.phone || 'N/A'}`,
      `Date: ${prescriptionDate.toLocaleDateString()}`,
    ]

    // Distribute patient details across full width
    const spacing = (pageWidth - 40) / (patientDetails.length - 1)
    patientDetails.forEach((detail, index) => {
      doc.text(detail, 20 + index * spacing, yPos)
    })

    yPos += 10

    // Add thin horizontal line
    doc.setLineWidth(0.05)
    doc.line(20, yPos, pageWidth - 20, yPos)

    return yPos + 10
  }

  /**
   * Add medicines table with columns matching screenshot
   */
  addMedicinesTable(doc, medicines) {
    const startY = 110

    const tableData = medicines.map((medicine, index) => [
      index + 1,
      medicine.drugForm || 'N/A',
      medicine.genericName || 'N/A',
      medicine.brandName || 'N/A',
      medicine.strength || 'N/A',
      medicine.dosage || medicine.quantity || 'N/A',
      medicine.frequency || 'N/A',
      medicine.duration || 'N/A',
      medicine.quantity || 'N/A',
      medicine.route || 'Oral',
      medicine.instructions || 'N/A',
      `${parseFloat(medicine.cost || 0).toFixed(2)}`,
    ])

    doc.autoTable({
      startY: startY,
      head: [
        [
          'No',
          'Drug Form',
          'Generic Name',
          'Brand Name',
          'Strength',
          'Nos/ml/gm',
          'Frequency',
          'Duration',
          'QTY',
          'Route',
          'Instructions',
          'Cost',
        ],
      ],
      body: tableData,
      theme: 'grid',
      headStyles: {
        fillColor: [50, 50, 50], // Dark background for header
        textColor: 255, // White text for header
        fontSize: 8,
        fontStyle: 'bold',
      },
      bodyStyles: {
        fontSize: 8,
        cellPadding: 2,
      },
      // Alternating row colors
      alternateRowStyles: {
        fillColor: [240, 240, 240], // Light gray for alternate rows
      },
      // Rounded corners
      styles: {
        lineWidth: 0.05,
        lineColor: [200, 200, 200],
        borderRadius: 5,
      },
      columnStyles: {
        0: { halign: 'center' },
        5: { halign: 'center' },
        8: { halign: 'center' },
        11: { halign: 'right' },
      },
      tableWidth: 'auto',
      margin: { left: 20, right: 20 },
    })
  }

  /**
   * Add footer section with signature, page number, total amount and disclaimer
   */
  async addFooterSection(doc, medicines) {
    const pageWidth = doc.internal.pageSize.width
    const finalY = doc.lastAutoTable.finalY || 200
    let yPos = finalY + 15

    // Left side - Digital signature (smaller size)
    if (fs.existsSync(this.signaturePath)) {
      try {
        const signatureData = fs.readFileSync(this.signaturePath, 'base64')
        doc.addImage(signatureData, 'PNG', 20, yPos, 40, 15)
      } catch (error) {
        console.warn('Could not load signature:', error.message)
      }
    }

    // Right side - Page number format "01 out of 01"
    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')
    doc.text('01 out of 01', pageWidth - 30, yPos + 10, { align: 'right' })

    yPos += 20

    // Add thin line
    doc.setLineWidth(0.05)
    doc.line(20, yPos, pageWidth - 20, yPos)

    yPos += 5

    // Total amount (right side) - no currency symbol
    const total = medicines.reduce(
      (sum, medicine) => sum + parseFloat(medicine.cost || 0),
      0,
    )
    doc.setFontSize(12)
    doc.setFont('helvetica', 'bold')
    doc.text(`Total Amount: ${total.toFixed(2)}`, pageWidth - 20, yPos, {
      align: 'right',
    })

    yPos += 8

    // Add another thin line
    doc.setLineWidth(0.05)
    doc.line(20, yPos, pageWidth - 20, yPos)

    yPos += 10

    // Disclaimer text - centered
    doc.setFontSize(9)
    doc.setFont('helvetica', 'normal')
    doc.text(
      "Medicines returned after 15 days won't be taken back.",
      pageWidth / 2,
      yPos,
      {
        align: 'center',
      },
    )
  }

  /**
   * Calculate age from date of birth
   */
  calculateAge(dob) {
    if (!dob) return null
    const birthDate = new Date(dob)
    const today = new Date()
    let age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--
    }

    return age
  }
}

module.exports = new PDFService()
