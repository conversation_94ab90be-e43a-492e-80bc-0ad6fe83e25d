const { jsPDF } = require('jspdf')
require('jspdf-autotable')
const path = require('path')
const fs = require('fs')

class PDFService {
  constructor() {
    this.logoPath = path.join(process.cwd(), 'assets', 'hospital-logo.png')
    this.signaturePath = path.join(
      process.cwd(),
      'assets',
      'digital-signature.png',
    )
  }

  /**
   * Generate prescription PDF
   * @param {Object} prescriptionData - Complete prescription data
   * @param {Object} patientData - Patient information
   * @param {Object} doctorData - Doctor information
   * @param {Object} organizationData - Organization information
   * @returns {Buffer} PDF buffer
   */
  async generatePrescriptionPDF(
    prescriptionData,
    patientData,
    doctorData,
    organizationData,
  ) {
    const doc = new jsPDF()

    // Set up document properties
    doc.setProperties({
      title: 'Medical Prescription',
      subject: 'Prescription',
      author: doctorData.general?.fullName || doctorData.name || 'Doctor',
      creator: '<PERSON><PERSON>i EMR System',
    })

    // Add header with logo and organization info
    await this.addHeader(doc, organizationData)

    // Add doctor and patient details
    this.addDoctorAndPatientDetails(doc, doctorData, patientData)

    // Add prescription date and ID
    this.addPrescriptionInfo(doc, prescriptionData)

    // Add medicines table
    this.addMedicinesTable(doc, prescriptionData.medicines)

    // Add total amount
    this.addTotalAmount(doc, prescriptionData.medicines)

    // Add digital signature
    await this.addDigitalSignature(doc, doctorData)

    // Add footer
    this.addFooter(doc)

    return doc.output('arraybuffer')
  }

  /**
   * Add header with logo and organization info
   */
  async addHeader(doc, organizationData) {
    const pageWidth = doc.internal.pageSize.width

    // Add logo if exists
    if (fs.existsSync(this.logoPath)) {
      try {
        const logoData = fs.readFileSync(this.logoPath, 'base64')
        doc.addImage(logoData, 'PNG', pageWidth - 50, 10, 40, 30)
      } catch (error) {
        console.warn('Could not load logo:', error.message)
      }
    }

    // Organization name and details
    doc.setFontSize(20)
    doc.setFont('helvetica', 'bold')
    doc.text(organizationData.name || 'Medical Center', 20, 25)

    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')
    if (organizationData.address) {
      const address =
        typeof organizationData.address === 'string'
          ? organizationData.address
          : `${organizationData.address.street || ''}, ${
              organizationData.address.city || ''
            }, ${organizationData.address.state || ''} ${
              organizationData.address.pinCode || ''
            }`
      doc.text(address, 20, 32)
    }

    if (organizationData.contactPhone) {
      doc.text(`Phone: ${organizationData.contactPhone}`, 20, 38)
    }

    if (organizationData.contactEmail) {
      doc.text(`Email: ${organizationData.contactEmail}`, 20, 44)
    }

    // Add horizontal line
    doc.setLineWidth(0.5)
    doc.line(20, 50, pageWidth - 20, 50)
  }

  /**
   * Add doctor and patient details
   */
  addDoctorAndPatientDetails(doc, doctorData, patientData) {
    const pageWidth = doc.internal.pageSize.width
    let yPos = 60

    // Doctor details (left side)
    doc.setFontSize(12)
    doc.setFont('helvetica', 'bold')
    doc.text('Doctor Details:', 20, yPos)

    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')
    yPos += 8
    doc.text(
      `Name: ${doctorData.general?.fullName || doctorData.name || 'N/A'}`,
      20,
      yPos,
    )
    yPos += 6
    doc.text(
      `Designation: ${doctorData.general?.designation || 'N/A'}`,
      20,
      yPos,
    )
    yPos += 6
    doc.text(`Department: ${doctorData.general?.department || 'N/A'}`, 20, yPos)
    yPos += 6
    doc.text(`Contact: ${doctorData.general?.contactNumber || 'N/A'}`, 20, yPos)
    yPos += 6
    doc.text(`Email: ${doctorData.general?.workEmail || 'N/A'}`, 20, yPos)

    // Patient details (right side)
    const rightColumnX = pageWidth / 2 + 10
    yPos = 60

    doc.setFontSize(12)
    doc.setFont('helvetica', 'bold')
    doc.text('Patient Details:', rightColumnX, yPos)

    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')
    yPos += 8
    doc.text(`Name: ${patientData.name || 'N/A'}`, rightColumnX, yPos)
    yPos += 6
    doc.text(
      `Age: ${this.calculateAge(patientData.dob) || 'N/A'}`,
      rightColumnX,
      yPos,
    )
    yPos += 6
    doc.text(`Gender: ${patientData.sex || 'N/A'}`, rightColumnX, yPos)
    yPos += 6
    doc.text(
      `Phone: ${patientData.contact?.phone || 'N/A'}`,
      rightColumnX,
      yPos,
    )
    yPos += 6
    doc.text(
      `Email: ${patientData.contact?.email || 'N/A'}`,
      rightColumnX,
      yPos,
    )

    // Add horizontal line
    doc.setLineWidth(0.5)
    doc.line(20, yPos + 10, pageWidth - 20, yPos + 10)
  }

  /**
   * Add prescription info
   */
  addPrescriptionInfo(doc, prescriptionData) {
    const pageWidth = doc.internal.pageSize.width
    let yPos = 120

    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')

    const prescriptionDate = new Date(
      prescriptionData.created_on || prescriptionData.createdAt || Date.now(),
    )
    doc.text(`Prescription ID: ${prescriptionData.id || 'N/A'}`, 20, yPos)
    doc.text(
      `Date: ${prescriptionDate.toLocaleDateString()}`,
      pageWidth - 80,
      yPos,
    )

    yPos += 10
    doc.setFontSize(14)
    doc.setFont('helvetica', 'bold')
    doc.text('PRESCRIPTION', pageWidth / 2, yPos, { align: 'center' })
  }

  /**
   * Add medicines table
   */
  addMedicinesTable(doc, medicines) {
    const tableData = medicines.map((medicine, index) => [
      index + 1,
      medicine.brandName || medicine.genericName || 'N/A',
      medicine.strength || 'N/A',
      medicine.frequency || 'N/A',
      medicine.duration || 'N/A',
      medicine.quantity || 'N/A',
      medicine.instructions || 'N/A',
      `₹${parseFloat(medicine.cost || 0).toFixed(2)}`,
    ])

    doc.autoTable({
      startY: 140,
      head: [
        [
          'S.No',
          'Medicine Name',
          'Strength',
          'Frequency',
          'Duration',
          'Quantity',
          'Instructions',
          'Cost',
        ],
      ],
      body: tableData,
      theme: 'grid',
      headStyles: {
        fillColor: [41, 128, 185],
        textColor: 255,
        fontSize: 10,
        fontStyle: 'bold',
      },
      bodyStyles: {
        fontSize: 9,
        cellPadding: 3,
      },
      columnStyles: {
        0: { cellWidth: 12, halign: 'center' },
        1: { cellWidth: 35 },
        2: { cellWidth: 20 },
        3: { cellWidth: 20 },
        4: { cellWidth: 20 },
        5: { cellWidth: 15, halign: 'center' },
        6: { cellWidth: 30 },
        7: { cellWidth: 20, halign: 'right' },
      },
      margin: { left: 20, right: 20 },
    })
  }

  /**
   * Add total amount
   */
  addTotalAmount(doc, medicines) {
    const total = medicines.reduce(
      (sum, medicine) => sum + parseFloat(medicine.cost || 0),
      0,
    )
    const finalY = doc.lastAutoTable.finalY || 200
    const pageWidth = doc.internal.pageSize.width

    doc.setFontSize(12)
    doc.setFont('helvetica', 'bold')
    doc.text(
      `Total Amount: ₹${total.toFixed(2)}`,
      pageWidth - 60,
      finalY + 15,
      { align: 'right' },
    )
  }

  /**
   * Add digital signature
   */
  async addDigitalSignature(doc, doctorData) {
    const pageWidth = doc.internal.pageSize.width
    const pageHeight = doc.internal.pageSize.height
    let yPos = pageHeight - 60

    // Add signature if exists
    if (fs.existsSync(this.signaturePath)) {
      try {
        const signatureData = fs.readFileSync(this.signaturePath, 'base64')
        doc.addImage(signatureData, 'PNG', pageWidth - 80, yPos - 20, 60, 20)
      } catch (error) {
        console.warn('Could not load signature:', error.message)
      }
    }

    // Doctor signature line and name
    doc.setLineWidth(0.5)
    doc.line(pageWidth - 80, yPos, pageWidth - 20, yPos)

    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')
    doc.text('Doctor Signature', pageWidth - 50, yPos + 8, { align: 'center' })
    doc.text(
      doctorData.general?.fullName || doctorData.name || 'Doctor',
      pageWidth - 50,
      yPos + 15,
      { align: 'center' },
    )
  }

  /**
   * Add footer
   */
  addFooter(doc) {
    const pageWidth = doc.internal.pageSize.width
    const pageHeight = doc.internal.pageSize.height

    doc.setFontSize(8)
    doc.setFont('helvetica', 'italic')
    doc.text('Generated by Arcaai EMR System', pageWidth / 2, pageHeight - 10, {
      align: 'center',
    })
  }

  /**
   * Calculate age from date of birth
   */
  calculateAge(dob) {
    if (!dob) return null
    const birthDate = new Date(dob)
    const today = new Date()
    let age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--
    }

    return age
  }
}

module.exports = new PDFService()
