/**
 * ABDM (Ayushman Bharat Digital Mission) Service
 * Handles integration with ABDM APIs for ABHA number generation and verification
 */

const axios = require('axios')
const { logInfo, logError } = require('../common/logging')
const NodeCache = require('node-cache')
const cache = new NodeCache({ checkperiod: 600 })

class ABDMService {
  constructor() {
    this.baseUrl = process.env.ABDM_API_BASE_URL || 'https://dev.abdm.gov.in/gateway'
    this.clientId = process.env.ABDM_CLIENT_ID
    this.clientSecret = process.env.ABDM_CLIENT_SECRET
    this.tokenEndpoint = `${this.baseUrl}/v0.5/sessions`
    this.apiEndpoints = {
      initRegistration: `${this.baseUrl}/v1/registration/init`,
      verifyOtp: `${this.baseUrl}/v1/auth/otp/verify`,
      confirmRegistration: `${this.baseUrl}/v1/registration/confirm`,
      fetchDetails: `${this.baseUrl}/v1/healthid/details`,
      verifyHealthId: `${this.baseUrl}/v1/verification/healthid`
    }
  }

  /**
   * Get access token for ABDM API
   * @returns {Promise<string>} Access token
   */
  async getAccessToken() {
    try {
      // Check if token exists in cache
      const cachedToken = cache.get('abdmToken')
      if (cachedToken) {
        return cachedToken
      }

      // Request new token
      const response = await axios.post(this.tokenEndpoint, {
        clientId: this.clientId,
        clientSecret: this.clientSecret
      })

      if (response.data && response.data.accessToken) {
        // Cache token for 55 minutes (assuming 1 hour validity)
        cache.set('abdmToken', response.data.accessToken, 55 * 60)
        return response.data.accessToken
      }

      throw new Error('Failed to obtain access token from ABDM')
    } catch (error) {
      logError('Error getting ABDM access token:', error)
      throw new Error(`ABDM authentication failed: ${error.message}`)
    }
  }

  /**
   * Initialize ABHA number registration process
   * @param {string} method - 'mobile' or 'aadhaar'
   * @param {string} value - Mobile number or Aadhaar number
   * @returns {Promise<Object>} Transaction details including txnId
   */
  async initiateRegistration(method, value) {
    try {
      const token = await this.getAccessToken()
      
      let payload = {}
      if (method === 'mobile') {
        payload = {
          mobile: value,
          authMethod: 'MOBILE_OTP'
        }
      } else if (method === 'aadhaar') {
        payload = {
          aadhaar: value,
          authMethod: 'AADHAAR_OTP'
        }
      } else {
        throw new Error('Invalid registration method. Use "mobile" or "aadhaar"')
      }

      const response = await axios.post(
        this.apiEndpoints.initRegistration,
        payload,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        }
      )

      logInfo(`ABDM registration initiated for ${method}`, { txnId: response.data.txnId })
      return response.data
    } catch (error) {
      logError(`Error initiating ABDM registration via ${method}:`, error)
      throw new Error(`Failed to initiate ABHA registration: ${error.response?.data?.message || error.message}`)
    }
  }

  /**
   * Verify OTP for ABHA registration
   * @param {string} txnId - Transaction ID from initiation step
   * @param {string} otp - OTP received by user
   * @returns {Promise<Object>} Verification result
   */
  async verifyOtp(txnId, otp) {
    try {
      const token = await this.getAccessToken()
      
      const response = await axios.post(
        this.apiEndpoints.verifyOtp,
        {
          txnId: txnId,
          otp: otp
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        }
      )

      logInfo('ABDM OTP verification successful', { txnId })
      return response.data
    } catch (error) {
      logError('Error verifying ABDM OTP:', error)
      throw new Error(`OTP verification failed: ${error.response?.data?.message || error.message}`)
    }
  }

  /**
   * Confirm ABHA registration after OTP verification
   * @param {string} txnId - Transaction ID from verification step
   * @param {Object} userData - User data for ABHA registration
   * @returns {Promise<Object>} ABHA details
   */
  async confirmRegistration(txnId, userData) {
    try {
      const token = await this.getAccessToken()
      
      const payload = {
        txnId: txnId,
        ...userData
      }

      const response = await axios.post(
        this.apiEndpoints.confirmRegistration,
        payload,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        }
      )

      logInfo('ABDM registration confirmed successfully', { 
        txnId,
        healthId: response.data.healthId
      })
      
      return response.data
    } catch (error) {
      logError('Error confirming ABDM registration:', error)
      throw new Error(`ABHA registration confirmation failed: ${error.response?.data?.message || error.message}`)
    }
  }

  /**
   * Fetch ABHA details using ABHA number or mobile
   * @param {string} type - 'healthId' or 'mobile'
   * @param {string} value - ABHA number or mobile number
   * @returns {Promise<Object>} ABHA details
   */
  async fetchAbhaDetails(type, value) {
    try {
      const token = await this.getAccessToken()
      
      let payload = {}
      if (type === 'healthId') {
        payload = { healthId: value }
      } else if (type === 'mobile') {
        payload = { mobile: value }
      } else {
        throw new Error('Invalid type. Use "healthId" or "mobile"')
      }

      const response = await axios.post(
        this.apiEndpoints.fetchDetails,
        payload,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        }
      )

      logInfo(`ABDM details fetched for ${type}`, { value })
      return response.data
    } catch (error) {
      logError(`Error fetching ABDM details for ${type}:`, error)
      throw new Error(`Failed to fetch ABHA details: ${error.response?.data?.message || error.message}`)
    }
  }

  /**
   * Verify ABHA number
   * @param {string} healthId - ABHA number to verify
   * @returns {Promise<Object>} Verification result
   */
  async verifyHealthId(healthId) {
    try {
      const token = await this.getAccessToken()
      
      const response = await axios.post(
        this.apiEndpoints.verifyHealthId,
        { healthId },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        }
      )

      logInfo('ABDM health ID verification successful', { healthId })
      return response.data
    } catch (error) {
      logError('Error verifying ABDM health ID:', error)
      throw new Error(`ABHA verification failed: ${error.response?.data?.message || error.message}`)
    }
  }
}

module.exports = new ABDMService()
