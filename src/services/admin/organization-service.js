const { logger } = require('@azure/identity')
const organizationRepository = require('../../repositories/admin/organization-repository')
const userRepository = require('../../repositories/admin/user-repository')
const bcrypt = require('bcrypt')
const { DefaultRoles } = require('../../common/roles')
const OrganizationModel = require('../../models/organization-model')
const roleRepository = require('../../repositories/admin/role-repository')
const rolePermissionService = require('../role-permission-service')
const userService = require('../user-service')
const auditLogger = require('../../common/audit-logger')
const { logInfo, logError } = require('../../common/logging')
const { generateSecurePassword } = require('../../utils/password-utils')
const b2cService = require('../b2c-service')

class OrganizationService {
  async seedSuperAdmin(superAdmin) {
    logger.info('Checking if Super Admin already exists...')
    const existingSuperAdmin = await userRepository.getUserByRole(
      DefaultRoles.SUPER_ADMIN,
    )

    if (existingSuperAdmin && existingSuperAdmin.length > 0) {
      logger.info('Super Admin already exists. Skipping seeding.')
      return
    }

    logger.info('Seeding Super Admin user...')
    const hashedPassword = await bcrypt.hash(superAdmin.password, 10)
    await userRepository.createSuperAdmin({
      ...superAdmin,
      password: hashedPassword,
    })
    logger.info('Super Admin created successfully.')
  }

  async createOrganization(data) {
    const organizationData = new OrganizationModel(data)
    const organization = await organizationRepository.createOrganization(
      organizationData,
    )

    // Create admin user object for local database (without password fields)
    const adminUserObj = {
      userRole: DefaultRoles.ORGANIZATION_ADMIN,
      name: organizationData.contactPersonName,
      email: organizationData.contactEmail,
      isActive: true,
      userType: DefaultRoles.ORGANIZATION_ADMIN,
      organizationId: organization.id,
      isOrganizationMainAdmin: true,
      created_by: 'system',
      updated_by: 'system',
    }

    // Remove password-related fields as they're handled by B2C
    delete adminUserObj.password
    delete adminUserObj.resetToken
    delete adminUserObj.resetTokenExpiry

    try {
      const localUser = await userService.addUser(adminUserObj)

      const temporaryPassword = generateSecurePassword()

      const b2cUser = {
        accountEnabled: true, // User is active but must change password
        displayName: organizationData.contactPersonName,
        identities: [
          {
            signInType: 'emailAddress',
            issuer: `${process.env.TENANT_NAME}.onmicrosoft.com`,
            issuerAssignedId: organizationData.contactEmail,
          },
        ],
        passwordProfile: {
          forceChangePasswordNextSignIn: true, // Force password change on first login
          password: temporaryPassword,
        },
        passwordPolicies: 'DisablePasswordExpiration, DisableStrongPassword',
      }

      try {
        const b2cResult = await b2cService.createB2CUser(b2cUser)
        logInfo(
          `B2C admin user created successfully for organization: ${organizationData.name}`,
        )

        // Update local user with B2C user ID
        localUser.b2cUserId = b2cResult.id
        await userService.updateUser(localUser)

        // Send welcome email with temporary password and B2C OAuth login link
        await b2cService.sendWelcomeEmailWithB2CSetup(
          organizationData.contactEmail,
          organizationData.contactPersonName,
          temporaryPassword,
          true, // isAdmin = true for organization admin creation
        )

        logInfo(
          `Organization admin B2C integration completed for: ${organizationData.contactEmail}`,
        )
      } catch (b2cError) {
        logError(
          `B2C integration failed for organization admin: ${organizationData.contactEmail}`,
          b2cError,
        )

        // Check for specific authentication errors
        if (
          b2cError.message &&
          b2cError.message.includes('Invalid client secret')
        ) {
          logError(
            'B2C authentication failed due to invalid client secret configuration',
            b2cError,
          )

          // Organization and local user are created, but B2C integration failed
          // We'll continue without B2C integration and notify about the issue
          logInfo(
            `Organization ${organizationData.name} created successfully, but B2C integration failed. Admin user exists locally but not in B2C.`,
          )

          // You might want to send a different notification or handle this case
          // For now, we'll continue without throwing an error
        } else {
          // For other B2C errors, re-throw to maintain existing behavior
          throw b2cError
        }
      }

      await auditLogger.logAction(
        'Organization Created with Admin B2C Integration',
        'system',
        {
          organizationId: organization.id,
          adminUserId: localUser.id,
          b2cUserId: b2cResult.id,
        },
      )

      logInfo(
        `Organization creation completed successfully: ${organizationData.name}`,
      )
    } catch (error) {
      logError(
        `Organization admin creation failed for: ${organizationData.contactEmail}`,
        error,
      )

      // Preserve GraphError and Microsoft Graph API error properties for proper handling upstream
      if (error.name === 'GraphError' || error.code === 'Request_BadRequest') {
        throw error // Re-throw error as-is
      }

      // For other errors, wrap them but preserve important properties
      const wrappedError = new Error(
        `Failed to create organization admin: ${error.message}`,
      )
      wrappedError.code = error.code || 'ORGANIZATION_ADMIN_CREATION_FAILED'
      wrappedError.statusCode = error.statusCode || 500
      wrappedError.originalError = error
      throw wrappedError
    }

    const defaultRoles = Object.keys(DefaultRoles)
      .filter((key) => key !== 'SUPER_ADMIN')
      .map((key) => ({
        id: `${organization.id}-${key}`,
        name: DefaultRoles[key],
        organizationId: organization.id,
        isDefault: true,
      }))

    for (const role of defaultRoles) {
      await roleRepository.createRole(role)

      const rolePermissionRecord = {
        id: role.id,
        roleName: role.name,
        organizationId: role.organizationId,
        APIs: [],
        created_on: new Date().toISOString(),
        updated_on: new Date().toISOString(),
      }
      await rolePermissionService.addRolePermission(rolePermissionRecord)
    }

    return { message: 'Organization and Admin created successfully' }
  }

  async editOrganization(data) {
    const organizationData = new OrganizationModel(data)
    if (data.status) {
      organizationData.status = data.status
    }
    return organizationRepository.updateOrganization(organizationData)
  }

  async listOrganizations(nameFilter, pageSize, pageNumber) {
    const result = await organizationRepository.getAllOrganizations(
      nameFilter,
      pageSize,
      pageNumber,
    )

    return {
      organizations: result.items,
      totalCount: result.totalCount,
      totalPages: result.totalPages,
      currentPage: pageNumber,
    }
  }

  async selectOrganization(superAdminId, organizationId) {
    const organization = await organizationRepository.getOrganizationById(
      organizationId,
    )
    if (!organization || !organization.isActive) {
      throw new Error('Invalid or inactive organization')
    }
    await userRepository.updateUserContext(superAdminId, { organizationId })
    return organization
  }

  async getOrganizationById(id) {
    return organizationRepository.getOrganizationById(id)
  }

  async deactivateOrganizationUsers(organizationId) {
    const users = await userRepository.getUsersByOrganizationId(organizationId)
    for (const user of users) {
      user.isActive = false
      await userRepository.updateUser(user.id, user)
    }
  }

  async checkOrganizationLinkedToUsers(organizationId) {
    const users = await userRepository.getUsersByOrganizationId(organizationId)
    return users.length > 0
  }

  async deleteOrganization(organizationId) {
    return await organizationRepository.deleteOrganization(organizationId)
  }
}

module.exports = new OrganizationService()
