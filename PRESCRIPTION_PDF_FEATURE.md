# Prescription PDF Generation Feature

## Overview

This feature allows generating and downloading prescription PDFs that match the provided screenshot layout exactly. The PDF includes all required sections with proper formatting and styling.

## Features

- **Download PDF**: Generate and download prescription as PDF file
- **Print PDF**: Generate prescription PDF for printing (inline display)
- **Professional Layout**: Matches the provided screenshot layout exactly
- **Dynamic Data**: Pulls data from Cosmos DB based on prescription, doctor, and organization IDs
- **Asset Support**: Includes hospital logo and digital signature (when available)

## API Endpoints

### Download Prescription PDF
```
GET /prescriptions/pdf/download?prescriptionId={id}&doctorId={id}&organizationId={id}
```

### Print Prescription PDF
```
GET /prescriptions/pdf/print?prescriptionId={id}&doctorId={id}&organizationId={id}
```

## PDF Layout

The generated PDF matches the provided screenshot layout exactly and includes:

1. **Header Section**
   - Left side: Doctor name, qualification, and registration number
   - Right side: Hospital logo (smaller size) and hospital address below it
   - Thin horizontal line separator

2. **Prescription Heading**
   - Centered "PRESCRIPTION" title
   - Patient information in one line: Name, ID, Age, Mobile, Date
   - Thin horizontal line separator

3. **Medicines Table**
   - 12 columns: No, Drug Name, Generic Name, Brand Name, Strength, Nos/ml/gm, Frequency, Duration, QTY, Route, Instructions, Cost
   - Professional table styling with auto-sizing
   - Proper alignment for different column types

4. **Footer Section**
   - Left side: Digital signature (smaller size)
   - Right side: Page number
   - Thin horizontal line
   - Total amount (right-aligned)
   - Another thin horizontal line
   - Disclaimer: "Medicines returned after 15 days won't be taken back"

## Data Sources

The PDF pulls data from the following Cosmos DB containers:

- **Prescriptions**: Prescription details and medicines list
- **PatientProfiles**: Patient information
- **DoctorProfiles**: Doctor information
- **Organizations**: Organization/hospital details

## Assets

Static assets are stored in the `/assets` folder:

- `hospital-logo.png`: Hospital logo (40x30 pixels recommended)
- `digital-signature.png`: Digital signature (60x20 pixels recommended)

## Dependencies

- `jspdf`: PDF generation library
- `jspdf-autotable`: Table generation for jsPDF

## Testing

Run the test script to verify PDF generation:

```bash
node test-pdf.js
```

This will generate a `test-prescription.pdf` file with mock data.

## Implementation Details

### Files Created/Modified

1. **src/services/pdf-service.js**: Core PDF generation service
2. **src/handlers/prescription-pdf-handler.js**: API request handler
3. **src/functions/prescription.js**: Added new API endpoints
4. **assets/**: Folder for static assets
5. **package.json**: Added jsPDF dependencies

### Error Handling

The API handles the following error cases:

- Missing required parameters
- Prescription not found
- Patient not found
- Doctor not found
- Organization not found
- PDF generation errors

### Response Format

**Success Response:**
- Status: 200
- Content-Type: application/pdf
- Content-Disposition: attachment/inline
- Body: PDF binary data

**Error Response:**
- Status: 400/404/500
- Content-Type: application/json
- Body: Error message

## Future Enhancements

1. **Dynamic Assets**: Store logos and signatures in database per organization/doctor
2. **Custom Templates**: Allow different PDF templates per organization
3. **Multi-language Support**: Support for different languages
4. **Digital Signatures**: Integration with digital signature services
5. **Batch Processing**: Generate multiple prescriptions at once
6. **Email Integration**: Send PDF via email
7. **Watermarks**: Add security watermarks
8. **QR Codes**: Add QR codes for verification

## Security Considerations

- Validate all input parameters
- Ensure proper authentication and authorization
- Sanitize data before PDF generation
- Implement rate limiting for PDF generation
- Log PDF generation activities for audit

## Performance Considerations

- PDF generation is CPU intensive
- Consider implementing caching for frequently accessed data
- Monitor memory usage for large prescriptions
- Implement async processing for batch operations
